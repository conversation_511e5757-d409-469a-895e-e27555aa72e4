<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.29.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="1749120516383-38" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <foreignKeyConstraintExists foreignKeyName="fk_san_pham_chi_tiet_on_am_thanh"/>
        </preConditions>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet" constraintName="fk_san_pham_chi_tiet_on_am_thanh"/>
    </changeSet>
    <changeSet id="1749120516383-39" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <foreignKeyConstraintExists foreignKeyName="fk_san_pham_chi_tiet_on_ban_phim"/>
        </preConditions>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet" constraintName="fk_san_pham_chi_tiet_on_ban_phim"/>
    </changeSet>
    <changeSet id="1749120516383-40" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <foreignKeyConstraintExists foreignKeyName="fk_san_pham_chi_tiet_on_bao_mat"/>
        </preConditions>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet" constraintName="fk_san_pham_chi_tiet_on_bao_mat"/>
    </changeSet>
    <changeSet id="1749120516383-41" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <foreignKeyConstraintExists foreignKeyName="fk_san_pham_chi_tiet_on_cong_giao_tiep"/>
        </preConditions>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet"
                                  constraintName="fk_san_pham_chi_tiet_on_cong_giao_tiep"/>
    </changeSet>
    <changeSet id="1749120516383-42" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <foreignKeyConstraintExists foreignKeyName="fk_san_pham_chi_tiet_on_he_dieu_hanh"/>
        </preConditions>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet"
                                  constraintName="fk_san_pham_chi_tiet_on_he_dieu_hanh"/>
    </changeSet>
    <changeSet id="1749120516383-43" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <foreignKeyConstraintExists foreignKeyName="fk_san_pham_chi_tiet_on_ket_noi_mang"/>
        </preConditions>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet"
                                  constraintName="fk_san_pham_chi_tiet_on_ket_noi_mang"/>
    </changeSet>
    <changeSet id="1749120516383-44" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <foreignKeyConstraintExists foreignKeyName="fk_san_pham_chi_tiet_on_pin"/>
        </preConditions>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet" constraintName="fk_san_pham_chi_tiet_on_pin"/>
    </changeSet>
    <changeSet id="1749120516383-45" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <foreignKeyConstraintExists foreignKeyName="fk_san_pham_chi_tiet_on_thiet_ke"/>
        </preConditions>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet" constraintName="fk_san_pham_chi_tiet_on_thiet_ke"/>
    </changeSet>
    <changeSet id="1749120516383-46" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <foreignKeyConstraintExists foreignKeyName="fk_san_pham_chi_tiet_on_webcam"/>
        </preConditions>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet" constraintName="fk_san_pham_chi_tiet_on_webcam"/>
    </changeSet>
    <changeSet id="1749120516383-12" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <not>
                <sequenceExists sequenceName="mau_sac_id_seq"/>
            </not>
        </preConditions>
        <createSequence incrementBy="1" sequenceName="mau_sac_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1749120516383-13" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <not>
                <sequenceExists sequenceName="serial_number_audit_history_id_seq"/>
            </not>
        </preConditions>
        <createSequence incrementBy="1" sequenceName="serial_number_audit_history_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1749120516383-14" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <not>
                <sequenceExists sequenceName="serial_number_id_seq"/>
            </not>
        </preConditions>
        <createSequence incrementBy="1" sequenceName="serial_number_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1749120516383-16" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="serial_number"/>
            </not>
        </preConditions>
        <createTable tableName="serial_number">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_serial_number"/>
            </column>
            <column name="ngay_tao" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_cap_nhat" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_tao" type="VARCHAR(100)"/>
            <column name="nguoi_cap_nhat" type="VARCHAR(100)"/>
            <column name="serial_number_value" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="san_pham_chi_tiet_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="AVAILABLE" name="trang_thai" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="thoi_gian_dat_truoc" type="DATETIME"/>
            <column name="kenh_dat_truoc" type="VARCHAR(20)"/>
            <column name="don_hang_dat_truoc" type="VARCHAR(50)"/>
            <column name="batch_number" type="VARCHAR(50)"/>
            <column name="ngay_san_xuat" type="DATETIME"/>
            <column name="ngay_het_bao_hanh" type="DATETIME"/>
            <column name="nha_cung_cap" type="VARCHAR(100)"/>
            <column name="import_batch_id" type="VARCHAR(50)"/>
            <column name="ghi_chu" type="VARCHAR(500)"/>
        </createTable>
    </changeSet>
    <changeSet id="1749120516383-17" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="serial_number_audit_history"/>
            </not>
        </preConditions>
        <createTable tableName="serial_number_audit_history">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_serial_number_audit_history"/>
            </column>
            <column name="serial_number_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="hanh_dong" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="thoi_gian_thay_doi" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_thuc_hien" type="VARCHAR(100)"/>
            <column name="ly_do_thay_doi" type="VARCHAR(500)"/>
            <column name="gia_tri_cu" type="jsonb"/>
            <column name="gia_tri_moi" type="jsonb"/>
            <column name="ip_address" type="VARCHAR(45)"/>
            <column name="user_agent" type="VARCHAR(500)"/>
            <column name="batch_operation_id" type="VARCHAR(50)"/>
            <column name="order_id" type="VARCHAR(50)"/>
            <column name="channel" type="VARCHAR(20)"/>
            <column name="metadata" type="jsonb"/>
        </createTable>
    </changeSet>
    <changeSet id="1749120516383-18" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="danh_muc" columnName="mo_ta_danh_muc"/>
            </not>
        </preConditions>
        <addColumn tableName="danh_muc">
            <column name="mo_ta_danh_muc" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet id="1749120516383-19" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="danh_muc" columnName="mo_ta_danh_muc"/>
            <not>
                <sqlCheck expectedResult="1">
                    SELECT COUNT(*) FROM information_schema.columns
                    WHERE table_name = 'danh_muc'
                    AND column_name = 'mo_ta_danh_muc'
                    AND is_nullable = 'NO'
                </sqlCheck>
            </not>
        </preConditions>
        <addNotNullConstraint columnName="mo_ta_danh_muc" tableName="danh_muc"/>
    </changeSet>
    <changeSet id="1749120516383-20" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="san_pham_chi_tiet" columnName="sku"/>
            </not>
        </preConditions>
        <addColumn tableName="san_pham_chi_tiet">
            <column name="sku" type="VARCHAR(100)"/>
        </addColumn>
    </changeSet>
    <changeSet id="1749120516383-21" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="san_pham_chi_tiet" columnName="sku"/>
            <not>
                <uniqueConstraintExists tableName="san_pham_chi_tiet" constraintName="uk_san_pham_chi_tiet_sku"/>
            </not>
        </preConditions>
        <addUniqueConstraint columnNames="sku" constraintName="uk_san_pham_chi_tiet_sku" tableName="san_pham_chi_tiet"/>
    </changeSet>
    <changeSet id="1749120516383-22" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="serial_number"/>
            <columnExists tableName="serial_number" columnName="serial_number_value"/>
            <not>
                <uniqueConstraintExists tableName="serial_number" constraintName="uk_serial_number_value"/>
            </not>
        </preConditions>
        <addUniqueConstraint columnNames="serial_number_value" constraintName="uk_serial_number_value"
                             tableName="serial_number"/>
    </changeSet>
    <changeSet id="1749120516383-23" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="serial_number_audit_history"/>
            <columnExists tableName="serial_number_audit_history" columnName="serial_number_id"/>
            <not>
                <indexExists indexName="idx_audit_serial_number_id"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_audit_serial_number_id" tableName="serial_number_audit_history">
            <column name="serial_number_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1749120516383-24" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="san_pham_chi_tiet"/>
            <columnExists tableName="san_pham_chi_tiet" columnName="trang_thai"/>
            <not>
                <indexExists indexName="idx_san_pham_chi_tiet_active"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_san_pham_chi_tiet_active" tableName="san_pham_chi_tiet">
            <column defaultValueBoolean="true" name="trang_thai"/>
        </createIndex>
    </changeSet>
    <changeSet id="1749120516383-25" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="san_pham_chi_tiet"/>
            <columnExists tableName="san_pham_chi_tiet" columnName="san_pham_id"/>
            <not>
                <indexExists indexName="idx_san_pham_chi_tiet_san_pham"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_san_pham_chi_tiet_san_pham" tableName="san_pham_chi_tiet">
            <column name="san_pham_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1749120516383-26" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="san_pham_chi_tiet"/>
            <columnExists tableName="san_pham_chi_tiet" columnName="sku"/>
            <not>
                <indexExists indexName="idx_san_pham_chi_tiet_sku"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_san_pham_chi_tiet_sku" tableName="san_pham_chi_tiet">
            <column name="sku"/>
        </createIndex>
    </changeSet>
    <changeSet id="1749120516383-27" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="serial_number_audit_history"/>
            <columnExists tableName="serial_number_audit_history" columnName="hanh_dong"/>
            <not>
                <indexExists indexName="idx_serial_number_audit_action"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_serial_number_audit_action" tableName="serial_number_audit_history">
            <column name="hanh_dong"/>
        </createIndex>
    </changeSet>
    <changeSet id="1749120516383-28" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="serial_number_audit_history"/>
            <columnExists tableName="serial_number_audit_history" columnName="batch_operation_id"/>
            <not>
                <indexExists indexName="idx_serial_number_audit_batch"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_serial_number_audit_batch" tableName="serial_number_audit_history">
            <column name="batch_operation_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1749120516383-29" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="serial_number_audit_history"/>
            <columnExists tableName="serial_number_audit_history" columnName="thoi_gian_thay_doi"/>
            <not>
                <indexExists indexName="idx_serial_number_audit_timestamp"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_serial_number_audit_timestamp" tableName="serial_number_audit_history">
            <column name="thoi_gian_thay_doi"/>
        </createIndex>
    </changeSet>
    <changeSet id="1749120516383-30" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="serial_number_audit_history"/>
            <columnExists tableName="serial_number_audit_history" columnName="nguoi_thuc_hien"/>
            <not>
                <indexExists indexName="idx_serial_number_audit_user"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_serial_number_audit_user" tableName="serial_number_audit_history">
            <column name="nguoi_thuc_hien"/>
        </createIndex>
    </changeSet>
    <changeSet id="1749120516383-31" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="serial_number"/>
            <columnExists tableName="serial_number" columnName="kenh_dat_truoc"/>
            <not>
                <indexExists indexName="idx_serial_number_channel"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_serial_number_channel" tableName="serial_number">
            <column name="kenh_dat_truoc"/>
        </createIndex>
    </changeSet>
    <changeSet id="1749120516383-32" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="serial_number"/>
            <columnExists tableName="serial_number" columnName="thoi_gian_dat_truoc"/>
            <not>
                <indexExists indexName="idx_serial_number_reservation"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_serial_number_reservation" tableName="serial_number">
            <column name="thoi_gian_dat_truoc"/>
        </createIndex>
    </changeSet>
    <changeSet id="1749120516383-33" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="serial_number"/>
            <columnExists tableName="serial_number" columnName="trang_thai"/>
            <not>
                <indexExists indexName="idx_serial_number_status"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_serial_number_status" tableName="serial_number">
            <column name="trang_thai"/>
        </createIndex>
    </changeSet>
    <changeSet id="1749120516383-34" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="serial_number"/>
            <columnExists tableName="serial_number" columnName="serial_number_value"/>
            <not>
                <indexExists indexName="idx_serial_number_value"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_serial_number_value" tableName="serial_number">
            <column name="serial_number_value"/>
        </createIndex>
    </changeSet>
    <changeSet id="1749120516383-36" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="san_pham_chi_tiet"/>
            <tableExists tableName="mau_sac"/>
            <columnExists tableName="san_pham_chi_tiet" columnName="mau_sac_id"/>
            <not>
                <foreignKeyConstraintExists foreignKeyName="FK_SAN_PHAM_CHI_TIET_ON_MAU_SAC"/>
            </not>
        </preConditions>
        <addForeignKeyConstraint baseColumnNames="mau_sac_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_MAU_SAC" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="mau_sac"/>
    </changeSet>
    <changeSet id="1749120516383-37" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="serial_number"/>
            <tableExists tableName="san_pham_chi_tiet"/>
            <columnExists tableName="serial_number" columnName="san_pham_chi_tiet_id"/>
            <not>
                <or>
                    <foreignKeyConstraintExists foreignKeyName="FK_SERIAL_NUMBER_ON_SAN_PHAM_CHI_TIET"/>
                    <indexExists indexName="idx_serial_number_variant"/>
                </or>
            </not>
        </preConditions>

        <addForeignKeyConstraint baseColumnNames="san_pham_chi_tiet_id" baseTableName="serial_number"
                                 constraintName="FK_SERIAL_NUMBER_ON_SAN_PHAM_CHI_TIET" onDelete="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="san_pham_chi_tiet"/>

        <createIndex indexName="idx_serial_number_variant" tableName="serial_number">
            <column name="san_pham_chi_tiet_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1749120516383-48" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="am_thanh"/>
        </preConditions>
        <dropTable cascadeConstraints="true" tableName="am_thanh"/>
    </changeSet>
    <changeSet id="1749120516383-49" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="ban_phim"/>
        </preConditions>
        <dropTable cascadeConstraints="true" tableName="ban_phim"/>
    </changeSet>
    <changeSet id="1749120516383-50" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="bao_mat"/>
        </preConditions>
        <dropTable cascadeConstraints="true" tableName="bao_mat"/>
    </changeSet>
    <changeSet id="1749120516383-51" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="cong_giao_tiep"/>
        </preConditions>
        <dropTable cascadeConstraints="true" tableName="cong_giao_tiep"/>
    </changeSet>
    <changeSet id="1749120516383-52" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="he_dieu_hanh"/>
        </preConditions>
        <dropTable cascadeConstraints="true" tableName="he_dieu_hanh"/>
    </changeSet>
    <changeSet id="1749120516383-53" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="ket_noi_mang"/>
        </preConditions>
        <dropTable cascadeConstraints="true" tableName="ket_noi_mang"/>
    </changeSet>
    <changeSet id="1749120516383-54" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="pin"/>
        </preConditions>
        <dropTable cascadeConstraints="true" tableName="pin"/>
    </changeSet>
    <changeSet id="1749120516383-55" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="thiet_ke"/>
        </preConditions>
        <dropTable cascadeConstraints="true" tableName="thiet_ke"/>
    </changeSet>
    <changeSet id="1749120516383-56" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="webcam"/>
        </preConditions>
        <dropTable cascadeConstraints="true" tableName="webcam"/>
    </changeSet>
    <changeSet id="1749120516383-60" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <or>
                <columnExists tableName="san_pham_chi_tiet" columnName="am_thanh_id"/>
                <columnExists tableName="san_pham_chi_tiet" columnName="ban_phim_id"/>
                <columnExists tableName="san_pham_chi_tiet" columnName="bao_mat_id"/>
                <columnExists tableName="san_pham_chi_tiet" columnName="cong_giao_tiep_id"/>
                <columnExists tableName="san_pham_chi_tiet" columnName="he_dieu_hanh_id"/>
                <columnExists tableName="san_pham_chi_tiet" columnName="kenh_dat_truoc"/>
                <columnExists tableName="san_pham_chi_tiet" columnName="ket_noi_mang_id"/>
                <columnExists tableName="san_pham_chi_tiet" columnName="pin_id"/>
                <columnExists tableName="san_pham_chi_tiet" columnName="serial_number"/>
                <columnExists tableName="san_pham_chi_tiet" columnName="thiet_ke_id"/>
                <columnExists tableName="san_pham_chi_tiet" columnName="thoi_gian_dat_truoc"/>
                <columnExists tableName="san_pham_chi_tiet" columnName="webcam_id"/>
            </or>
        </preConditions>
        <dropColumn columnName="am_thanh_id" tableName="san_pham_chi_tiet"/>

        <dropColumn columnName="ban_phim_id" tableName="san_pham_chi_tiet"/>

        <dropColumn columnName="bao_mat_id" tableName="san_pham_chi_tiet"/>

        <dropColumn columnName="cong_giao_tiep_id" tableName="san_pham_chi_tiet"/>

        <dropColumn columnName="he_dieu_hanh_id" tableName="san_pham_chi_tiet"/>

        <dropColumn columnName="ket_noi_mang_id" tableName="san_pham_chi_tiet"/>

        <dropColumn columnName="pin_id" tableName="san_pham_chi_tiet"/>

        <dropColumn columnName="serial_number" tableName="san_pham_chi_tiet"/>

        <dropColumn columnName="thiet_ke_id" tableName="san_pham_chi_tiet"/>

        <dropColumn columnName="webcam_id" tableName="san_pham_chi_tiet"/>

        <dropColumn columnName="trang_thai" tableName="san_pham_chi_tiet"/>
    </changeSet>
    <changeSet id="1749120516383-64" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="dot_giam_gia" columnName="da_an"/>
        </preConditions>
        <dropColumn columnName="da_an" tableName="dot_giam_gia"/>
    </changeSet>
    <changeSet id="1749120516383-69" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <or>
                <columnExists tableName="danh_muc" columnName="ma_danh_muc"/>
                <columnExists tableName="danh_muc" columnName="ten_danh_muc"/>
            </or>
        </preConditions>
        <dropColumn columnName="ma_danh_muc" tableName="danh_muc"/>

        <dropColumn columnName="ten_danh_muc" tableName="danh_muc"/>
    </changeSet>
    <changeSet id="1749120516383-76" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <sequenceExists sequenceName="am_thanh_id_seq"/>
        </preConditions>
        <dropSequence sequenceName="am_thanh_id_seq"/>
    </changeSet>
    <changeSet id="1749120516383-77" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <sequenceExists sequenceName="ban_phim_id_seq"/>
        </preConditions>
        <dropSequence sequenceName="ban_phim_id_seq"/>
    </changeSet>
    <changeSet id="1749120516383-78" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <sequenceExists sequenceName="bao_mat_id_seq"/>
        </preConditions>
        <dropSequence sequenceName="bao_mat_id_seq"/>
    </changeSet>
    <changeSet id="1749120516383-79" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <sequenceExists sequenceName="cong_giao_tiep_id_seq"/>
        </preConditions>
        <dropSequence sequenceName="cong_giao_tiep_id_seq"/>
    </changeSet>
    <changeSet id="1749120516383-80" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <sequenceExists sequenceName="he_dieu_hanh_id_seq"/>
        </preConditions>
        <dropSequence sequenceName="he_dieu_hanh_id_seq"/>
    </changeSet>
    <changeSet id="1749120516383-81" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <sequenceExists sequenceName="ket_noi_mang_id_seq"/>
        </preConditions>
        <dropSequence sequenceName="ket_noi_mang_id_seq"/>
    </changeSet>
    <changeSet id="1749120516383-82" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <sequenceExists sequenceName="pin_id_seq"/>
        </preConditions>
        <dropSequence sequenceName="pin_id_seq"/>
    </changeSet>
    <changeSet id="1749120516383-83" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <sequenceExists sequenceName="thiet_ke_id_seq"/>
        </preConditions>
        <dropSequence sequenceName="thiet_ke_id_seq"/>
    </changeSet>
    <changeSet id="1749120516383-84" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <sequenceExists sequenceName="webcam_id_seq"/>
        </preConditions>
        <dropSequence sequenceName="webcam_id_seq"/>
    </changeSet>
    <changeSet id="1749120516383-2" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="san_pham_chi_tiet"/>
            <not>
                <columnExists tableName="san_pham_chi_tiet" columnName="trang_thai"/>
            </not>
        </preConditions>
        <addColumn tableName="san_pham_chi_tiet">
            <column defaultValueBoolean="true" name="trang_thai" type="BOOLEAN">
                <constraints nullable="false" validateNullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="1749120516383-3" author="obscurites">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="san_pham_chi_tiet"/>
            <columnExists tableName="san_pham_chi_tiet" columnName="trang_thai"/>
        </preConditions>
        <addDefaultValue columnDataType="boolean" columnName="trang_thai" defaultValueBoolean="true"
                         tableName="san_pham_chi_tiet"/>
    </changeSet>

</databaseChangeLog>